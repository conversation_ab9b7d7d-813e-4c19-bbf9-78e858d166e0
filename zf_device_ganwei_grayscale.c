/*********************************************************************************************************************
* MSPM0G3507 Opensource Library 即（MSPM0G3507 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 MSPM0G3507 开源库的一部分
* 
* MSPM0G3507 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          zf_device_ganwei_grayscale
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          MDK 5.37
* 适用平台          MSPM0G3507
* 店铺链接          https://seekfree.taobao.com/
*
* 修改记录
* 日期              作者                备注
* 2025-01-01       SeekFree            感为8路灰度传感器驱动
********************************************************************************************************************/

#include "zf_device_ganwei_grayscale.h"
#include "zf_driver_gpio.h"
#include "zf_driver_adc.h"
#include "zf_driver_delay.h"
#include "zf_common_debug.h"
#include "string.h"

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     感为8路灰度传感器 采集8个通道的模拟值并进行均值滤波
// 参数说明     dev_info        传感器信息结构体指针
// 参数说明     result          存储8个通道处理结果的数组
// 返回参数     void
// 使用示例     ganwei_grayscale_get_analog_values(&sensor_info, analog_result);
// 备注信息     内部函数，用户无需直接调用
//-------------------------------------------------------------------------------------------------------------------
static void ganwei_grayscale_get_analog_values (ganwei_grayscale_info_struct *dev_info, uint16 *result)
{
    uint8 i, j;
    uint32 analog_sum = 0;
    
    // 遍历8个传感器通道（3位地址线组合）
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        // 通过地址线组合切换传感器通道（注意取反逻辑）
        gpio_set_level(dev_info->addr_pin0, !(i & 0x01));  // 地址线0，对应bit0
        gpio_set_level(dev_info->addr_pin1, !(i & 0x02));  // 地址线1，对应bit1  
        gpio_set_level(dev_info->addr_pin2, !(i & 0x04));  // 地址线2，对应bit2
        
        // 每个通道采集8次ADC值进行均值滤波
        analog_sum = 0;
        for(j = 0; j < 8; j++)
        {
            analog_sum += adc_convert(dev_info->adc_pin);  // 累加ADC采样值
        }
        
        // 根据方向设置存储结果
        if(!dev_info->direction_reverse)
        {
            result[i] = analog_sum / 8;  // 计算平均值
        }
        else
        {
            result[7 - i] = analog_sum / 8;  // 反向存储
        }
    }
}//-------------------------------------------------------------------------------------------------------------------
// 函数简介     感为8路灰度传感器 将模拟值转换为数字信号（二值化处理）
// 参数说明     dev_info        传感器信息结构体指针
// 返回参数     void
// 使用示例     ganwei_grayscale_convert_to_digital(&sensor_info);
// 备注信息     内部函数，用户无需直接调用
//-------------------------------------------------------------------------------------------------------------------
static void ganwei_grayscale_convert_to_digital (ganwei_grayscale_info_struct *dev_info)
{
    uint8 i;
    
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        if(dev_info->analog_value[i] > dev_info->gray_white[i])
        {
            dev_info->digital_value |= (1 << i);   // 超过白阈值置1（白色）
        }
        else if(dev_info->analog_value[i] < dev_info->gray_black[i])
        {
            dev_info->digital_value &= ~(1 << i);  // 低于黑阈值置0（黑色）
        }
        // 中间灰度值保持原有状态
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     感为8路灰度传感器 归一化ADC值到指定范围
// 参数说明     dev_info        传感器信息结构体指针
// 返回参数     void
// 使用示例     ganwei_grayscale_normalize_values(&sensor_info);
// 备注信息     内部函数，用户无需直接调用
//-------------------------------------------------------------------------------------------------------------------
static void ganwei_grayscale_normalize_values (ganwei_grayscale_info_struct *dev_info)
{
    uint8 i;
    uint16 normalized_value;
    
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        // 计算归一化值（减去黑电平后缩放）
        if(dev_info->analog_value[i] < dev_info->calibrated_black[i])
        {
            normalized_value = 0;  // 低于黑电平归零
        }
        else
        {
            normalized_value = (uint16)((dev_info->analog_value[i] - dev_info->calibrated_black[i]) * dev_info->normal_factor[i]);
        }
        
        // 限幅处理
        if(normalized_value > dev_info->adc_max_value)
        {
            normalized_value = (uint16)dev_info->adc_max_value;
        }
        
        dev_info->normal_value[i] = normalized_value;
    }
}//-------------------------------------------------------------------------------------------------------------------
// 函数简介     感为8路灰度传感器 初始化
// 参数说明     dev_info        传感器信息结构体指针
// 参数说明     edition         传感器版本
// 参数说明     adc_bits        ADC分辨率
// 参数说明     addr0           地址线0引脚
// 参数说明     addr1           地址线1引脚
// 参数说明     addr2           地址线2引脚
// 参数说明     adc_ch          ADC通道
// 返回参数     uint8           初始化状态 0-失败 1-成功
// 使用示例     ganwei_grayscale_init(&sensor_info, GANWEI_GRAYSCALE_CLASS_EDITION, GANWEI_GRAYSCALE_ADC_12BITS, A0, A1, A2, ADC_IN_CH0);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
uint8 ganwei_grayscale_init (ganwei_grayscale_info_struct *dev_info, ganwei_grayscale_edition_enum edition, ganwei_grayscale_adc_bits_enum adc_bits, gpio_pin_enum addr0, gpio_pin_enum addr1, gpio_pin_enum addr2, adc_pin_enum adc_pin)
{
    uint8 i;
    
    // 清零所有数据
    memset(dev_info->analog_value, 0, sizeof(dev_info->analog_value));
    memset(dev_info->normal_value, 0, sizeof(dev_info->normal_value));
    memset(dev_info->calibrated_white, 0, sizeof(dev_info->calibrated_white));
    memset(dev_info->calibrated_black, 0, sizeof(dev_info->calibrated_black));
    memset(dev_info->gray_white, 0, sizeof(dev_info->gray_white));
    memset(dev_info->gray_black, 0, sizeof(dev_info->gray_black));
    
    // 初始化归一化系数
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        dev_info->normal_factor[i] = 0.0f;
    }
    
    // 保存硬件配置
    dev_info->edition = edition;
    dev_info->adc_resolution = adc_bits;
    dev_info->addr_pin0 = addr0;
    dev_info->addr_pin1 = addr1;
    dev_info->addr_pin2 = addr2;
    dev_info->adc_pin = adc_pin;
    dev_info->direction_reverse = 0;  // 默认不反向
    
    // 设置ADC最大值
    switch(adc_bits)
    {
        case GANWEI_GRAYSCALE_ADC_8BITS:  dev_info->adc_max_value = 255.0f;   break;
        case GANWEI_GRAYSCALE_ADC_10BITS: dev_info->adc_max_value = 1023.0f;  break;
        case GANWEI_GRAYSCALE_ADC_12BITS: dev_info->adc_max_value = 4095.0f;  break;
        case GANWEI_GRAYSCALE_ADC_14BITS: dev_info->adc_max_value = 16383.0f; break;
        default: dev_info->adc_max_value = 4095.0f; break;
    }
    
    // 设置超时时间
    if(edition == GANWEI_GRAYSCALE_CLASS_EDITION)
    {
        dev_info->timeout_value = 1;  // 经典版1ms
    }
    else
    {
        dev_info->timeout_value = 10; // 青春版10ms
    }
    
    // 初始化状态变量
    dev_info->digital_value = 0;
    dev_info->tick_counter = 0;
    dev_info->init_flag = 0;  // 标记未完成校准
    
    // 初始化GPIO引脚
    gpio_init(addr0, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(addr1, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(addr2, GPO, GPIO_LOW, GPO_PUSH_PULL);
    
    // 初始化ADC
    adc_init(adc_pin, ADC_12BIT);

    return 1;
}//-------------------------------------------------------------------------------------------------------------------
// 函数简介     感为8路灰度传感器 带校准参数的初始化
// 参数说明     dev_info        传感器信息结构体指针
// 参数说明     white_values    白校准值数组
// 参数说明     black_values    黑校准值数组
// 返回参数     uint8           初始化状态 0-失败 1-成功
// 使用示例     ganwei_grayscale_init_with_calibration(&sensor_info, white_array, black_array);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
uint8 ganwei_grayscale_init_with_calibration (ganwei_grayscale_info_struct *dev_info, uint16 *white_values, uint16 *black_values)
{
    uint8 i;
    uint16 temp;
    float normal_diff;
    
    // 处理校准数据
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        // 确保白值 > 黑值（必要时交换）
        if(black_values[i] >= white_values[i])
        {
            temp = white_values[i];
            white_values[i] = black_values[i];
            black_values[i] = temp;
        }
        
        // 计算灰度阈值（1:2和2:1分界点）
        dev_info->gray_white[i] = (white_values[i] * 2 + black_values[i]) / 3;
        dev_info->gray_black[i] = (white_values[i] + black_values[i] * 2) / 3;
        
        // 保存校准数据
        dev_info->calibrated_black[i] = black_values[i];
        dev_info->calibrated_white[i] = white_values[i];
        
        // 处理无效校准数据（全黑/全白/相等情况）
        if((white_values[i] == 0 && black_values[i] == 0) || (white_values[i] == black_values[i]))
        {
            dev_info->normal_factor[i] = 0.0f;  // 无效通道
            continue;
        }
        
        // 计算归一化系数
        normal_diff = (float)white_values[i] - (float)black_values[i];
        dev_info->normal_factor[i] = dev_info->adc_max_value / normal_diff;
    }
    
    dev_info->init_flag = 1;  // 标记初始化完成
    return 1;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     感为8路灰度传感器 主任务处理
// 参数说明     dev_info        传感器信息结构体指针
// 返回参数     void
// 使用示例     ganwei_grayscale_task(&sensor_info);
// 备注信息     需要在主循环中定期调用，建议1ms调用一次
//-------------------------------------------------------------------------------------------------------------------
void ganwei_grayscale_task (ganwei_grayscale_info_struct *dev_info)
{
    // 采集模拟量数据
    ganwei_grayscale_get_analog_values(dev_info, dev_info->analog_value);
    
    // 如果已完成校准，进行数字化和归一化处理
    if(dev_info->init_flag)
    {
        ganwei_grayscale_convert_to_digital(dev_info);
        ganwei_grayscale_normalize_values(dev_info);
    }
}//-------------------------------------------------------------------------------------------------------------------
// 函数简介     感为8路灰度传感器 获取数字信号状态
// 参数说明     dev_info        传感器信息结构体指针
// 返回参数     uint8           8位数字状态（每位对应一个传感器）
// 使用示例     uint8 digital = ganwei_grayscale_get_digital(&sensor_info);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
uint8 ganwei_grayscale_get_digital (ganwei_grayscale_info_struct *dev_info)
{
    return dev_info->digital_value;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     感为8路灰度传感器 获取模拟量数据
// 参数说明     dev_info        传感器信息结构体指针
// 参数说明     result          存储结果的数组指针
// 返回参数     uint8           获取状态 0-失败 1-成功
// 使用示例     uint16 analog_data[8]; ganwei_grayscale_get_analog(&sensor_info, analog_data);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
uint8 ganwei_grayscale_get_analog (ganwei_grayscale_info_struct *dev_info, uint16 *result)
{
    uint8 i;
    
    // 重新采集数据
    ganwei_grayscale_get_analog_values(dev_info, dev_info->analog_value);
    
    // 拷贝数据到结果数组
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        result[i] = dev_info->analog_value[i];
    }
    
    return 1;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     感为8路灰度传感器 获取归一化数据
// 参数说明     dev_info        传感器信息结构体指针
// 参数说明     result          存储结果的数组指针
// 返回参数     uint8           获取状态 0-未初始化 1-成功
// 使用示例     uint16 normal_data[8]; ganwei_grayscale_get_normalized(&sensor_info, normal_data);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
uint8 ganwei_grayscale_get_normalized (ganwei_grayscale_info_struct *dev_info, uint16 *result)
{
    uint8 i;
    
    if(!dev_info->init_flag)
    {
        return 0;  // 未完成校准初始化
    }
    
    // 拷贝归一化数据到结果数组
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        result[i] = dev_info->normal_value[i];
    }
    
    return 1;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     感为8路灰度传感器 设置输出方向
// 参数说明     dev_info        传感器信息结构体指针
// 参数说明     reverse         是否反向 0-正向 1-反向
// 返回参数     void
// 使用示例     ganwei_grayscale_set_direction(&sensor_info, 1);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
void ganwei_grayscale_set_direction (ganwei_grayscale_info_struct *dev_info, uint8 reverse)
{
    dev_info->direction_reverse = reverse;
}